-target:library
-out:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll"
-refout:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ref.dll"
-define:UNITY_6000_2_6
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:UNITY_INPUT_SYSTEM_ENABLE_VR
-define:UNITY_INPUT_SYSTEM_ENABLE_XR
-define:UNITY_INPUT_SYSTEM_ENABLE_PHYSICS
-define:UNITY_INPUT_SYSTEM_ENABLE_PHYSICS2D
-define:UNITY_INPUT_SYSTEM_ENABLE_UI
-define:HAS_SET_LOCAL_POSITION_AND_ROTATION
-define:UNITY_INPUT_SYSTEM_PROJECT_WIDE_ACTIONS
-define:UNITY_INPUT_SYSTEM_INPUT_ACTIONS_EDITOR_AUTO_SAVE_ON_FOCUS_LOST
-define:UNITY_INPUT_SYSTEM_PLATFORM_SCROLL_DELTA
-define:UNITY_INPUT_SYSTEM_INPUT_MODULE_SCROLL_DELTA
-define:UNITY_INPUT_SYSTEM_INPUT_MODULE_NAVIGATION_DEVICE_TYPE
-define:UNITY_INPUT_SYSTEM_INPUT_MODULE_NAVIGATION_DEVICE_TYPE
-define:UNITY_INPUT_SYSTEM_SENDPOINTERHOVERTOPARENT
-define:UNITY_INPUT_SYSTEM_ENABLE_ANALYTICS
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.InAppPurchasingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.LevelPlayModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.2.6f2/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/AxisComposite.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/ButtonWithOneModifier.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/ButtonWithTwoModifiers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/OneModifierComposite.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/TwoModifiersComposite.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/Vector2Composite.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Composites/Vector3Composite.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/IInputActionCollection.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/IInputInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputAction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionAsset.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionChange.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionMap.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionParameters.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionPhase.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionProperty.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionRebindingExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionReference.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionSetupExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionState.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionTrace.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputActionType.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputBinding.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputBindingComposite.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputBindingCompositeContext.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputBindingResolver.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputControlScheme.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/InputInteractionContext.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Interactions/HoldInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Interactions/MultiTapInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Interactions/PressInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Interactions/SlowTapInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Actions/Interactions/TapInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/AssemblyInfo.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/AnyKeyControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/AxisControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/ButtonControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/CommonUsages.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/DeltaControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/DiscreteButtonControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/DoubleControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/DpadControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlAttribute.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlLayout.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlLayoutAttribute.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlLayoutChange.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlList.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputControlPath.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/InputProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/IntegerControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/KeyControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/AxisDeadzoneProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/ClampProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/CompensateDirectionProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/CompensateRotationProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/EditorWindowSpaceProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/InvertProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/InvertVector2Processor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/InvertVector3Processor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/NormalizeProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/NormalizeVector2Processor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/NormalizeVector3Processor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/ScaleProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/ScaleVector2Processor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/ScaleVector3Processor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Processors/StickDeadzoneProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/QuaternionControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/StickControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/TouchControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/TouchPhaseControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/TouchPressControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Vector2Control.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Controls/Vector3Control.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/DisableDeviceCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/EnableDeviceCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/EnableIMECompositionCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/IInputDeviceCommandInfo.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/InitiateUserAccountPairingCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/InputDeviceCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryCanRunInBackground.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryDimensionsCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryEditorWindowCoordinatesCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryEnabledStateCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryKeyboardLayoutCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryKeyNameCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryPairedUserAccountCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QuerySamplingFrequencyCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/QueryUserIdCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/RequestResetCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/RequestSyncCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/SetIMECursorPositionCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/SetSamplingFrequencyCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Commands/WarpMousePositionCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Gamepad.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Haptics/DualMotorRumble.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Haptics/DualMotorRumbleCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Haptics/IDualMotorRumble.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Haptics/IHaptics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/ICustomDeviceReset.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/IEventMerger.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/IEventPreProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/IInputUpdateCallbackReceiver.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/InputDevice.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/InputDeviceBuilder.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/InputDeviceChange.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/InputDeviceDescription.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/InputDeviceMatcher.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/ITextInputReceiver.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Joystick.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Keyboard.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Mouse.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Pen.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Pointer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Precompiled/FastKeyboard.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Precompiled/FastMouse.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Precompiled/FastMouse.partial.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Precompiled/FastTouchscreen.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Remote/InputRemoting.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Remote/RemoteInputPlayerConnection.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Sensor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/Touchscreen.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Devices/TrackedDevice.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/InputActionsEditorSessionAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/InputBuildAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/InputComponentEditorAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/InputEditorAnalytics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/InputExitPlayModeAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/OnScreenStickEditorAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/PlayerInputEditorAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/PlayerInputManagerEditorAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Analytics/VirtualMouseInputEditorAnalytic.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputActionAssetManager.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputActionEditorToolbar.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputActionEditorWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputActionPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputActionTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputActionTreeViewItems.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/InputBindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/NameAndParameterListView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/ParameterListView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetEditor/PropertiesViewBase.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetImporter/IInputActionAssetEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetImporter/InputActionAssetEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetImporter/InputActionAssetIconLoader.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetImporter/InputActionCodeGenerator.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetImporter/InputActionImporter.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/AssetImporter/InputActionImporterEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/BuildPipeline/LinkFileGenerator.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/IInputControlPickerLayout.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/InputControlDropdownItem.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/InputControlPathEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/InputControlPicker.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/InputControlPickerDropdown.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/InputControlPickerState.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/Layouts/DefaultInputControlPickerLayout.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ControlPicker/Layouts/TouchscreenControlPickerLayout.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Debugger/InputActionDebuggerWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Debugger/InputDebuggerWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Debugger/InputDeviceDebuggerWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Debugger/InputLatencyCalculator.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Debugger/SampleFrequencyCalculator.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/DeviceSimulator/InputSystemPlugin.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Dialog.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/DownloadableSample.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/EditorInputControlLayoutCache.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/InputAssetEditorUtils.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/InputDiagnostics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/InputLayoutCodeGenerator.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/InputParameterEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/InputSystemPackageControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/InputSystemPluginControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdown.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownDataSource.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownGUI.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownItem.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownState.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/CallbackDataSource.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/AdvancedDropdown/MultiLevelDataSource.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/BuildProviderHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/EditorHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/GUIHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/InputActionSerializationHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/InputControlTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/InputEventTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/InputStateWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/SerializedPropertyHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/SerializedPropertyLinqExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Internal/TreeViewHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ProjectWideActions/ProjectWideActionsAsset.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/ProjectWideActions/ProjectWideActionsBuildProvider.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/GamepadButtonPropertyDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionAssetDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionAssetSearchProvider.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionDrawerBase.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionMapDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionPropertyDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionReferencePropertyDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputActionReferenceSearchProviders.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/PropertyDrawers/InputControlPathDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Settings/EditorPlayerSettingHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Settings/InputEditorUserSettings.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Settings/InputSettingsBuildProvider.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/Settings/InputSettingsProvider.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Commands/Commands.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Commands/ControlSchemeCommands.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/EnumerableExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/ExpressionUtils.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/InputActionsEditorConstants.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/InputActionsEditorSettingsProvider.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/InputActionsEditorState.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/InputActionsEditorWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/InputActionsEditorWindowUtils.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/ReactiveProperty.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/SerializedInputAction.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/SerializedInputActionMap.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/SerializedInputBinding.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/StateContainer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ActionMapsView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ActionPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ActionsTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/BindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/CollectionViewSelectionChangeFilter.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/CompositeBindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/CompositePartBindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ContextMenu.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ControlSchemesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/CopyPasteHelper.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/DropManipulator.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/InputActionMapsTreeViewItem.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/InputActionsEditorView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/InputActionsTreeViewItem.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/IViewStateCollection.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/MatchingControlPaths.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/NameAndParametersListView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/PropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/Selectors.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ViewBase.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/ViewStateCollection.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Editor/UITKAssetEditor/Views/VisualElementExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/ActionEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/DeltaStateEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/DeviceConfigurationEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/DeviceRemoveEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/DeviceResetEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/IInputEventTypeInfo.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/IMECompositionEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/InputEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/InputEventBuffer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/InputEventListener.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/InputEventPtr.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/InputEventStream.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/InputEventTrace.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/StateEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Events/TextEvent.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/IInputDiagnostics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/IInputRuntime.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputAnalytics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputFeatureNames.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputManager.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputManagerStateMonitors.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputMetrics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputSettings.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputSystem.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputSystemObject.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/InputUpdateType.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/NativeInputRuntime.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Android/AndroidAxis.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Android/AndroidGameController.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Android/AndroidKeyCode.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Android/AndroidSensors.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Android/AndroidSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/DualShock/DualShockGamepad.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/DualShock/DualShockGamepadHID.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/DualShock/DualShockSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/DualShock/IDualShockHaptics.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/EnhancedTouch/EnhancedTouchSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/EnhancedTouch/Finger.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/EnhancedTouch/Touch.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/EnhancedTouch/TouchHistory.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/EnhancedTouch/TouchSimulation.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/HID/HID.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/HID/HIDDescriptorWindow.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/HID/HIDParser.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/HID/HIDSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/iOS/InputSettingsiOS.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/iOS/InputSettingsiOSProvider.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/iOS/IOSGameController.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/iOS/iOSPostProcessBuild.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/iOS/iOSStepCounter.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/iOS/iOSSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Linux/LinuxSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Linux/SDLDeviceBuilder.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/OnScreen/OnScreenButton.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/OnScreen/OnScreenControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/OnScreen/OnScreenStick.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/OnScreen/OnScreenSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/OSX/OSXGameController.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/OSX/OSXSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/DefaultInputActions.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/InputValue.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/PlayerInput.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/PlayerInputEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/PlayerInputManager.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/PlayerInputManagerEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/PlayerJoinBehavior.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/PlayerInput/PlayerNotifications.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Steam/IStreamControllerAPI.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Steam/SteamController.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Steam/SteamControllerType.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Steam/SteamHandle.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Steam/SteamIGAConverter.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Steam/SteamSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Switch/SwitchProControllerHID.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Switch/SwitchSupportHID.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/BaseInputOverride.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/ExtendedAxisEventData.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/ExtendedPointerEventData.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/ExtendedSubmitCancelEventData.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/INavigationEventData.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/InputSystemUIInputModule.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/InputSystemUIInputModuleEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/MultiplayerEventSystem.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/NavigationModel.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/PointerModel.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/StandaloneInputModuleEditor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/TrackedDeviceRaycaster.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/UISupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UI/VirtualMouseInput.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/UnityRemote/UnityRemoteSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/WebGL/WebGLGamepad.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/WebGL/WebGLJoystick.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/WebGL/WebGLSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XInput/IXboxOneRumble.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XInput/XboxGamepadMacOS.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XInput/XInputController.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XInput/XInputControllerWindows.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XInput/XInputSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Controls/PoseControl.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Devices/GoogleVR.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Devices/Oculus.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Devices/OpenVR.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Devices/WindowsMR.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/GenericXRDevice.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Haptics/BufferedRumble.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Haptics/GetCurrentHapticStateCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Haptics/GetHapticCapabilitiesCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Haptics/SendBufferedHapticsCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/Haptics/SendHapticImpulseCommand.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/TrackedPoseDriver.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/XRLayoutBuilder.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Plugins/XR/XRSupport.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/IInputStateCallbackReceiver.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/IInputStateChangeMonitor.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/IInputStateTypeInfo.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/InputState.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/InputStateBlock.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/InputStateBuffers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/State/InputStateHistory.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/ArrayHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/CallbackArray.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Comparers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/CSharpCodeHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/DelegateHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/DisplayStringFormatAttribute.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/DynamicBitfield.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/ExceptionHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/FourCC.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/InlinedArray.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/InternedString.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/JsonParser.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/MemoryHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/MiscHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/NameAndParameters.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/NamedValue.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/NumberHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/ForDeviceEventObservable.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/Observable.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/Observer.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/SelectManyObservable.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/SelectObservable.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/TakeNObservable.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Observables/WhereObservable.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/OneOrMore.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/PredictiveParser.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/PrimitiveValue.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/ReadOnlyArray.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/SavedState.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/SpriteUtilities.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/StringHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/Substring.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/TypeHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5/InputSystem/Utilities/TypeTable.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.UnityAdditionalFile.txt"