# AI Navigation preferences reference

Use the AI Navigation preferences to specify how the navigation meshes (NavMesh and HeightMesh) display in the Scene view.

The AI Navigation preferences are located in the [Preferences window](xref:Preferences). To open the AI Navigation preferences, do the following:

1. In the main menu, go to **Edit** &gt; **Preferences**.
1. Select **AI Navigation**.

 The following table describes the controls available in the AI Navigation preferences tab.

| **Control**                     | **Description**                    |
|:--------------------------------|:-----------------------------------|
| **Selected Surfaces Opacity**   | Specify the opacity of the displayed meshes (NavMesh and HeightMesh) for [NavMesh Surface](./NavMeshSurface.md) instances that are part of the current selection hierarchy.    |
| **Unselected Surfaces Opacity** | Specify the opacity of displayed meshes (NavMesh and HeightMesh) for [NavMesh Surface](./NavMeshSurface.md) instances that are outside of the current selection hierarchy. |
| **Height Mesh Color**           | Set the color used to display the [HeightMesh](./HeightMesh.md).                                           |
| **Reset to Defaults**           | Set all the NavMesh Visualization Settings parameters to their default value. |

> [!Note]
> The NavMesh is represented in the colors described in the [Areas tab](./NavigationWindow.md#areas-tab) of the Navigation window. That color palette cannot be modified.

## Additional resources
- [Preferences window](xref:Preferences)
- [AI Navigation overlay](./NavigationOverlay.md)
